import React from 'react'
import { update } from '@src/common/images'
import './style.less'

interface UpdateVersionProps {
  latestVersion: string
}
const UpdateVersion: React.FC<UpdateVersionProps> = ({ latestVersion }) => {
  const handleDownloadClick = async () => {
    try {
      const downloadUrl = `http://webassist.sit.saas.htsc/webAssistant/index.html#/download?version=${latestVersion}`;
      await chrome.tabs.create({
        url: downloadUrl,
      })
    } catch (error) {
      console.error('Failed to open download page:', error)
      console.error('Download page navigation failed')
    }
  }
  return (
    <div className="updateVersionContainer">
      <img className="updateImg" src={update} alt="update" />
      <div className="title">插件版本更新</div>
      <div className="downloadBtn" onClick={handleDownloadClick}>点击下载</div>
    </div>
  )
}

export default UpdateVersion
