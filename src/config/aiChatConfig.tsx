import { handleTranslateCurrentPage, handleSummaryCurrentPage, getUrlsInfo } from '../sidepanel/components/AiChat/utils'
import { translate, summarize, more, condenser, expander, polisher, corrector, ocr, toImage } from '@/src/common/images'
import { ImgIcon } from '@/src/common/Icons'
import { buildApiUrl } from '../common/utils/baseUrl'
import { DEFAULT_TRANSLATE_OPTIONS } from './languageConfig';
import { getUserId } from '@src/common/utils/userConfigApi';
import { getWikiAuth } from '@src/common/utils/wikiAuth';
import { message } from 'antd';

// 定义 skill 配置函数的参数类型
interface SkillConfigParams {
  messageApi?: any;
  chatUiRef?: any;
}
const PROJECTID = process.env.PLASMO_TAG === 'prod' ? 'cmfksb3yk07pebt07dtjs9gsm' : 'cmcm7wf6s04lcbr07xgvbjutp' // 测试环境和生产环境使用同一个项目ID
// 将 skill 改为函数，接收 messageApi 和 chatUiRef 参数
const createSkillConfig = (params: SkillConfigParams = {}) => {
  const { messageApi, chatUiRef } = params;

  return [
    {
      key: '',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={translate} width={13} height={14} />,// 图标
      label: '翻译',// 文案
      question: '翻译上面文字',// 发送时的命令
      agentId: '1',//发送时可指定智能体ID
      children: [
        {
          key: 'translate',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: 'AI翻译',// 文案
          question: 'AI翻译',// 发送时的命令
          agentId: 'translate',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
          translateOptions: DEFAULT_TRANSLATE_OPTIONS,
        },
        {
          key: 'translate-page',// 必传，唯一标识
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '翻译此页面',// 文案
          question: '翻译此页面',// 发送时的命令
          agentId: 'translate-page',//发送时可指定智能体ID
          // children?: Skill[],//折叠子项,有子项时，点击会展开子项
          // expandIcon?: string,// 折叠图标
          // customRender?: React.ReactNode,//自定义渲染
          onClick: () => handleTranslateCurrentPage(messageApi),// 技能点击回调 -》 由AiChat统一写入Composer
        }
      ],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'summary',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={summarize} />,// 图标
      label: '网页总结',// 文案
      question: '网页总结',// 发送时的命令
      agentId: 'summary',//发送时可指定智能体ID
      // children?: Skill[],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => handleSummaryCurrentPage(messageApi, chatUiRef),// 技能点击回调 -》 由AiChat统一写入Composer
      // getUrlsInfo: getUrlsInfo,
    },
    {
      key: 'default',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={ocr} />,// 图标
      label: '图片文字提取',// 文案
      question: '图片文字提取',// 发送时的命令
      showUpload: true,// 是否显示上传入口
      agentId: 'image-recognition',//发送时可指定智能体ID
      // onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'textToImage',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={toImage} />,// 图标
      label: '文生图',// 文案
      question: '文生图',// 发送时的命令
      showUpload: true,// 是否显示上传入口
      agentId: 'textToImage',//发送时可指定智能体ID
      // onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'text-condenser',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={condenser} />,// 图标
      label: '缩写',// 文案
      question: '缩写',// 发送时的命令
      agentId: 'text-condenser',//发送时可指定智能体ID
      // children?: Skill[],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'text-expander',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={expander} />,// 图标
      label: '扩写',// 文案
      question: '扩写',// 发送时的命令
      agentId: 'text-expander',//发送时可指定智能体ID
      // children?: Skill[],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'text-polisher',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={polisher} />,// 图标
      label: '润色',// 文案
      question: '润色',// 发送时的命令
      agentId: 'text-polisher',//发送时可指定智能体ID
      // children?: Skill[],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    },
    {
      key: 'grammar-corrector',// 必传，唯一标识
      disabled: false,//是否禁用
      icon: <ImgIcon src={corrector} />,// 图标
      label: '修正拼写和语法',// 文案
      question: '修正拼写和语法',// 发送时的命令
      agentId: 'grammar-corrector',//发送时可指定智能体ID
      // children?: Skill[],//折叠子项,有子项时，点击会展开子项
      // expandIcon?: string,// 折叠图标
      // customRender?: React.ReactNode,//自定义渲染
      onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    }
    // {
    //   key: 'more',// 必传，唯一标识
    //   disabled: false,//是否禁用
    //   icon: <ImgIcon src={more} />,// 图标
    //   label: '更多',// 文案
    //   question: '',// 发送时的命令
    //   agentId: '1',//发送时可指定智能体ID
    //   children: [
    //     {
    //       key: 'default',// 必传，唯一标识
    //       disabled: false,//是否禁用
    //       icon: <ImgIcon src={ocr} />,// 图标
    //       label: '图片文字提取',// 文案
    //       question: '图片文字提取',// 发送时的命令
    //       showUpload: true,// 是否显示上传入口
    //       agentId: 'image-recognition',//发送时可指定智能体ID
    //       // onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    //     },
    //     {
    //       key: 'text-condenser',// 必传，唯一标识
    //       disabled: false,//是否禁用
    //       icon: <ImgIcon src={condenser} />,// 图标
    //       label: '缩写',// 文案
    //       question: '缩写',// 发送时的命令
    //       agentId: 'text-condenser',//发送时可指定智能体ID
    //       // children?: Skill[],//折叠子项,有子项时，点击会展开子项
    //       // expandIcon?: string,// 折叠图标
    //       // customRender?: React.ReactNode,//自定义渲染
    //       onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    //     },
    //     {
    //       key: 'text-expander',// 必传，唯一标识
    //       disabled: false,//是否禁用
    //       icon: <ImgIcon src={expander} />,// 图标
    //       label: '扩写',// 文案
    //       question: '扩写',// 发送时的命令
    //       agentId: 'text-expander',//发送时可指定智能体ID
    //       // children?: Skill[],//折叠子项,有子项时，点击会展开子项
    //       // expandIcon?: string,// 折叠图标
    //       // customRender?: React.ReactNode,//自定义渲染
    //       onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    //     },
    //     {
    //       key: 'text-polisher',// 必传，唯一标识
    //       disabled: false,//是否禁用
    //       icon: <ImgIcon src={polisher} />,// 图标
    //       label: '润色',// 文案
    //       question: '润色',// 发送时的命令
    //       agentId: 'text-polisher',//发送时可指定智能体ID
    //       // children?: Skill[],//折叠子项,有子项时，点击会展开子项
    //       // expandIcon?: string,// 折叠图标
    //       // customRender?: React.ReactNode,//自定义渲染
    //       onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    //     },
    //     {
    //       key: 'grammar-corrector',// 必传，唯一标识
    //       disabled: false,//是否禁用
    //       icon: <ImgIcon src={corrector} />,// 图标
    //       label: '修正拼写和语法',// 文案
    //       question: '修正拼写和语法',// 发送时的命令
    //       agentId: 'grammar-corrector',//发送时可指定智能体ID
    //       // children?: Skill[],//折叠子项,有子项时，点击会展开子项
    //       // expandIcon?: string,// 折叠图标
    //       // customRender?: React.ReactNode,//自定义渲染
    //       onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
    //     }
    //   ]
    // }
  ];
}

// 创建 composerConfig 函数，接收 messageApi 和 chatUiRef 参数
const createComposerConfig = (params: SkillConfigParams = {}) => {
  const skill = createSkillConfig(params);

  return {
    // aboveNode: <AboveNode chatUiRef={chatUiRef} />,
    // placeholder: getPlaceholder(),
    quoteOperations: {
      citetext: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译',// 发送时的命令 --》 拼装
        agentId: 'translate',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '总结',// 文案
        question: '总结',// 发送时的命令 --》 拼装
        agentId: 'summary',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '扩写',// 文案
        question: '扩写',// 发送时的命令 --》 拼装
        agentId: 'text-expander',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '缩写',// 文案
        question: '缩写',// 发送时的命令 --》 拼装
        agentId: 'abbreviation',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '润色',// 文案
        question: '润色',// 发送时的命令 --》 拼装
        agentId: 'text-polisher',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '修正拼写和语法',// 文案
        question: '修正拼写和语法',// 发送时的命令 --》 拼装
        agentId: 'grammar-corrector',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],
      citeweb: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],

      image: [
        {
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '图片解释',// 文案
          question: '图片解释',// 发送时的命令 --》 拼装
          agentId: 'image-recognition',//发送时可指定智能体ID --》拼装
          customRender: '',//自定义渲染
        },
        {
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '图片文字提取',// 文案
          question: '图片文字提取',// 发送时的命令 --》 拼装
          agentId: 'image-recognition',//发送时可指定智能体ID --》拼装
          customRender: '',//自定义渲染
        },
      ],

      file: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '文件解析',// 文案
        question: '解析该文件',// 发送时的命令 --》 拼装
        agentId: '',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],
      excel: [{
        disabled: false,
        icon: '',
        label: '内容总结',
        question: '总结文档内容',
        agentId: '',
        customRender: '',
      }],
      word: [{
        disabled: false,
        icon: '',
        label: '内容总结',
        question: '总结文档内容',
        agentId: '',
        customRender: '',
      },
      {
        disabled: false,
        icon: '',
        label: '润色',
        question: '润色该文档',
        agentId: '',
        customRender: '',
      },
        // {
        //   disabled: false,
        //   icon: '',
        //   label: '精炼',
        //   question: '精炼该文档',
        //   agentId: '',
        //   customRender: '',
        // },
      ],
      ppt: [{
        disabled: false,
        icon: '',
        label: '内容总结',
        question: '总结文档内容',
        agentId: '',
        customRender: '',
      },
      {
        disabled: false,
        icon: '',
        label: '润色',
        question: '润色该文档',
        agentId: '',
        customRender: '',
      },
        // {
        //   disabled: false,
        //   icon: '',
        //   label: '精炼',
        //   question: '精炼该文档',
        //   agentId: '',
        //   customRender: '',
        // },
      ],
      pdf: [{
        disabled: false,
        icon: '',
        label: '内容总结',
        question: '总结文档内容',
        agentId: '',
        customRender: '',
      },
      {
        disabled: false,
        icon: '',
        label: '润色',
        question: '润色该文档',
        agentId: '',
        customRender: '',
      },
        // {
        //   disabled: false,
        //   icon: '',
        //   label: '精炼',
        //   question: '精炼该文档',
        //   agentId: '',
        //   customRender: '',
        // },
      ],
    },
    skill,
    quickNewConversation: true,  //对话框显示新建对话
    quickOpenHistory: true,  // 对话框显示会话历史
    uploadConfig: {
      action: buildApiUrl('/fileService/uploadFile'),
      // maxCount: 1,
      headers: {
        'Iv-User': '002332'
      },
      // accept: '.pdf,.doc,.docx,.ppt,.pptx,.txt,.csv,.xls,.xlsx,.json,.png,.jpg,.jpeg',
      accept: '.pdf,.doc,.docx,.ppt,.pptx,.csv,.xls,.xlsx,.json,.png,.jpg,.jpeg',
      getDownloadAction: buildApiUrl('/fileService/getDownloadUrl'),
      downloadAction: buildApiUrl('/fileService/downloadFile'),
    },
    showSkillMarket: true,
    openSkillMarket: () => {
      // message.success('技能市场功能开发中，敬请期待')
      gotoSkillsMarket()
    }
  };
}

// 打开配置页面
const gotoSkillsMarket = async () => {
  try {
    // 在新标签页中打开配置页面
    // 使用与upgrade页面相同的路径格式
    const settingsUrl = `chrome-extension://${chrome.runtime.id}/tabs/skillsmarket.html`

    console.log('Opening settings URL:', settingsUrl)
    await chrome.tabs.create({
      url: settingsUrl,
    })
  } catch (error) {
    console.error('Failed to open settings page:', error)
    // 如果打开新标签页失败，则显示错误信息
    console.error('Settings page navigation failed')
  }
}

// 创建配置对象的异步函数
const createConfig = (userId) => {
  return {
    //租户id：表示当前系统
    appId: 'web-assistant',

    //用户id：代表当前系统唯一用户id
    userId: userId,

    requests: {
      baseUrl() {
        return ``
      },
      init: {
        // type: 'http',//请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
        // action:'27006',//移动端如果走tcp，需要传接口请求action号
        // paramsKey：'MS__REQUEST__PAYLOAD'，//移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
        url: buildApiUrl('/session/createSession'),
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',

        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选
        // requestTransfer: (input: object) => { //同步写法
        // requestTransfer:async (input: object) => { //支持异步写法
        //   const parsedInput = {
        //     ...input,
        //     customerInput1: '123',
        //   }
        //   return parsedInput;
        // },
        // responseTransfer: (output: object) => {
        //   const parsedOutput = {
        //     ...output,
        //     customeroutput1: '123',
        //   }
        //   return parsedOutput;
        // },
      },


      //问答接口
      send: {
        url: buildApiUrl('/chat/workflow/chrome-v2'),
        createConversationUrl: buildApiUrl('/session/createSession'),
        isAibag: true,
        stream: true,
        messageInterval: 50,
        async requestTransfer(params) {
          // 获取Wiki认证信息
          let wikiCookie = '';
          try {
            const wikiAuth = await getWikiAuth();
            if (wikiAuth && wikiAuth.cookie) {
              wikiCookie = wikiAuth.cookie;
            }
          } catch (error) {
            console.error('获取Wiki认证信息失败:', error);
          }

          // 添加searchOptions字段
          const enhancedParams = {
            ...params,
            projectId: PROJECTID,
            searchOptions: {
              enableInternetSearch: true,
              provider: ['hiagent', 'wiki'],
              snippet: false,
              authInfos: {
                wiki: {
                  cookie: wikiCookie
                }
              }
            },
            // translateOptions: {

            // }
          };

          return enhancedParams;
        },
      },
      //查询历史详情接口
      history: {
        url: buildApiUrl('/session/getHistoryMessages'),
      },
      //点赞点踩接口
      score: {
        url: buildApiUrl('/session/feedback'),
      },
      //停止生成接口
      stop: {
        url: buildApiUrl('/session/interruptSession'),
      },

      // 历史会话列表
      historyConversation: {
        url: buildApiUrl('/session/getHistorySessions'),
        requestTransfer: async (input: object) => { //支持异步写法
          const parsedInput = {
            ...input,
            queryHideConversation: false,
          }
          return parsedInput;
        },
      },

      deleteHistoryConversation: {
        url: buildApiUrl('/session/deleteSession'),
      },

      deleteMessage: {
        url: buildApiUrl('/session/deleteMessage'),
      },

      // 创建新会话接口
      createConversation: {
        url: buildApiUrl('/session/createSession'),
      },

    },
  }
}

// 为了向后兼容，保留同步的默认配置
const defaultConfig = {
  //租户id：表示当前系统
  appId: 'web-assistant',

  //用户id：代表当前系统唯一用户id
  userId: 'anonymous_user',

  requests: {
    baseUrl() {
      return ``
    },
    init: {
      url: buildApiUrl('/session/createSession'),
      headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },
    },
    send: {
      url: buildApiUrl('/chat/workflow/chrome-v2'),
      createConversationUrl: buildApiUrl('/session/createSession'),
      isAibag: true,
      stream: true,
      messageInterval: 50,
    },
    history: {
      url: buildApiUrl('/session/getHistoryMessages'),
    },
    score: {
      url: buildApiUrl('/session/feedback'),
    },
    stop: {
      url: buildApiUrl('/session/interruptSession'),
    },
    historyConversation: {
      url: buildApiUrl('/session/getHistorySessions'),
      requestTransfer: async (input: object) => {
        const parsedInput = {
          ...input,
          queryHideConversation: false,
        }
        return parsedInput;
      },
    },
    deleteHistoryConversation: {
      url: buildApiUrl('/session/deleteSession'),
    },
    deleteMessage: {
      url: buildApiUrl('/session/deleteMessage'),
    },
    createConversation: {
      url: buildApiUrl('/session/createSession'),
    },
  },
}

export {
  createSkillConfig,
  createComposerConfig,
  createConfig,
  defaultConfig
};
