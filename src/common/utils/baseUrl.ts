const DOMAINS = {
    prod: 'eip.htsc.com.cn/webassist',
    test: 'webassist.saassit.htsc.com.cn'
} as const;

const CONFIG = {
    // 优先使用 PLASMO_TAG，如果没有则使用 PLASMO_PUBLIC_ENV，最后使用 NODE_ENV
    DOMAIN: (() => {
        const env = process.env.PLASMO_TAG;
        return env === 'prod' ? DOMAINS.prod : DOMAINS.test;
    })(),
    DEFAULT_PROTOCOL: 'https',
    EXTENSION_PROTOCOL: 'http'
} as const;

console.log('Environment check:', {
    PLASMO_TAG: process.env.PLASMO_TAG,
    selectedDomain: CONFIG.DOMAIN
});

/**
 * Get base URL based on current environment
 */
export const getBaseUrl = (): string => {
    const protocol =
        typeof window !== 'undefined' &&
            window.location.protocol === 'chrome-extension:'
            ? CONFIG.EXTENSION_PROTOCOL
            : CONFIG.DEFAULT_PROTOCOL;

    return `${protocol}://${CONFIG.DOMAIN}`;
};

/**
 * Build full API URL with given path
 */
export const buildApiUrl = (path: string): string => {
    return `${getBaseUrl()}${path}`;
};