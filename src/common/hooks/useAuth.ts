import { useState, useEffect } from 'react'
import { userStorage } from '@src/common/utils'
import { checkLoginStatus } from '@src/sidepanel/utils'
import { authStorage } from '@src/common/utils/authStorage'

export const useAuth = () => {
  const [isCompleted, setIsCompleted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const initializeAuthState = async () => {
      setIsLoading(true)

      // 首先检查本地存储是否有用户信息
      const localUserInfo = await userStorage.getUserInfo()
      if (localUserInfo) {
        setIsCompleted(true)
        setIsLoading(false)
        // 后台验证登录状态，如果失败则清除本地数据
        checkLoginStatus(async (isLoggedIn, data) => {
          if (!isLoggedIn) {
            await userStorage.clearUserInfo()
            setIsCompleted(false)
          } else if (data) {
            // 更新用户信息
            await userStorage.setUserInfo(data)
          }
        })
        return
      }

      // 如果本地没有用户信息，则进行网络验证
      checkLoginStatus(async (isLoggedIn, data) => {
        if (isLoggedIn && data) {
          // 保存到本地存储
          await userStorage.setUserInfo(data)
          setIsCompleted(true)
        }
        setIsLoading(false)
      })
    }

    initializeAuthState()

    // 监听来自 background 的登录成功消息和登出消息
    const messageListener = async (message, sender, sendResponse) => {
      if (message.type === 'loginSuccess') {
        checkLoginStatus(async (isLoggedIn, data) => {
          if (isLoggedIn && data) {
            // 保存到本地存储
            await userStorage.setUserInfo(data)
            setIsCompleted(true)
            console.log('登录成功，更新用户信息:', data)

            // 关闭登录 tab
            if (message.payload && message.payload.tabId) {
              chrome.tabs.remove(message.payload.tabId)
            }

            // 跳转回原始tab
            try {
              const result = await chrome.storage.session.get('originalTabId')
              if (result.originalTabId) {
                console.log('跳转回原始tab:', result.originalTabId)
                await chrome.tabs.update(result.originalTabId, { active: true })
                // 清除保存的原始tab ID
                await chrome.storage.session.remove('originalTabId')
              }
            } catch (error) {
              console.warn('跳转回原始tab失败:', error)
            }
          }
        })
      } else if (message.type === 'logoutSuccess') {
        // 处理登出逻辑
        console.log('检测到登出，清空用户数据')

        // 清空本地存储的用户信息
        await userStorage.clearUserInfo()

        // 清空认证信息（包括wiki认证信息）
        await authStorage.clearAuthInfo()

        // 清除相关 cookie
        chrome.cookies.remove({
          url: 'http://eipuat.htsc.com.cn',
          name: 'EIPGW-TOKEN'
        })

        // 更新登录状态，显示登录页面
        setIsCompleted(false)

        console.log('登出完成，已清空用户数据')
      }
    }

    chrome.runtime.onMessage.addListener(messageListener)

    // 清理监听器
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener)
    }
  }, [])

  return { isCompleted, isLoading }
}

export default useAuth
