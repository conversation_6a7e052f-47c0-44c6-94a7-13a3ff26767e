<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本提取测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .content-txt {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        span {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
        }
        h2 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        p {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>网页翻译文本提取测试页面</h1>
    
    <div class="test-section">
        <div class="test-title">测试场景1：混合内容容器（目标场景）</div>
        <div class="content-txt" id="content-txt">
            <a href="#">链接1</a>
            作文是英语学习的重要内容之一，通过写作可以提高语言表达能力。
            <span>span内容</span>
            <h2>标题</h2>
            Some English paragraph content that should be translated together with other text.
            <p>段落内容</p>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景2：纯文本节点</div>
        <div class="content-txt">
            这是一段纯文本内容，没有任何HTML标签包裹。
            这应该被识别为一个完整的翻译单元。
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景3：嵌套结构</div>
        <div class="content-txt">
            <div>
                外层文本内容
                <div>
                    内层文本内容
                    <span>内联元素文本</span>
                    更多内层文本
                </div>
                外层结束文本
            </div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景4：列表结构</div>
        <div class="content-txt">
            <ul>
                <li>列表项1：这是第一个列表项的内容</li>
                <li>列表项2：这是第二个列表项的内容</li>
                <li>列表项3：这是第三个列表项的内容</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景5：表格结构</div>
        <div class="content-txt">
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <tr>
                    <td>单元格1：这是表格中的文本内容</td>
                    <td>单元格2：另一个单元格的文本</td>
                </tr>
                <tr>
                    <td>单元格3：第二行第一列</td>
                    <td>单元格4：第二行第二列</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景6：复杂混合内容</div>
        <div class="content-txt">
            <h3>文章标题</h3>
            这是文章的开头段落，包含了一些重要信息。
            <p>这是一个正式的段落，<strong>包含加粗文本</strong>和<em>斜体文本</em>。</p>
            文章中间的过渡文本。
            <blockquote>
                这是一个引用块，通常用来引用其他来源的内容。
            </blockquote>
            文章结尾的总结文本。
        </div>
    </div>

    <script>
        // 简单的测试脚本，用于验证文本提取
        console.log('测试页面已加载');
        
        // 模拟文本提取测试
        function testTextExtraction() {
            const containers = document.querySelectorAll('.content-txt');
            containers.forEach((container, index) => {
                console.log(`\n=== 测试容器 ${index + 1} ===`);
                console.log('容器HTML:', container.innerHTML);
                console.log('容器文本:', container.textContent?.trim());
                
                // 模拟TreeWalker遍历
                const walker = document.createTreeWalker(
                    container,
                    NodeFilter.SHOW_TEXT,
                    {
                        acceptNode: function(node) {
                            return node.textContent?.trim() ? 
                                NodeFilter.FILTER_ACCEPT : 
                                NodeFilter.FILTER_REJECT;
                        }
                    }
                );
                
                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    textNodes.push({
                        content: node.textContent?.trim(),
                        parent: node.parentElement?.tagName
                    });
                }
                
                console.log('提取的文本节点:', textNodes);
            });
        }
        
        // 页面加载完成后执行测试
        window.addEventListener('load', () => {
            setTimeout(testTextExtraction, 1000);
        });
    </script>
</body>
</html>
